[{"id": "17538415442982", "fileName": "17538415442982.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\17538415442982.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_002", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-31", "06:33"], "SummaryContent": "Invitation to the quarterly team meeting.", "isRead": false, "EncryptedFrom": "XFSQ9YAb7vKHdYh565HtRBUmk/wfIsm8NFBT01tzn0A="}, {"id": "17538415442984", "fileName": "17538415442984.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\17538415442984.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_004", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "13:49"], "SummaryContent": "Meeting reminder: Project update discussion.", "isRead": false, "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "17538415442987", "fileName": "17538415442987.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\17538415442987.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_007", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "15:17"], "SummaryContent": "Please confirm your availability for the event.", "isRead": false, "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": 47, "fileName": "valid_005.json", "filePath": "C:\\classifyMail\\ReviewMail\\valid_005.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_005", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-31", "09:46"], "SummaryContent": "This is a sample email for testing purposes.", "isRead": true, "readAt": "2025-07-31T07:45:15.367Z", "EncryptedFrom": "<EMAIL>"}, {"id": "17538415442988", "fileName": "17538415442988.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\17538415442988.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_008", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-31", "12:14"], "SummaryContent": "Thank you for your continued support.", "isRead": false, "EncryptedFrom": "<EMAIL>"}, {"id": 47, "fileName": "valid_009.json", "filePath": "C:\\classifyMail\\ReviewMail\\valid_009.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_009", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "09:17"], "SummaryContent": "This is a sample email for testing purposes.", "isRead": false, "EncryptedFrom": "<EMAIL>"}, {"id": "175384154429810", "fileName": "175384154429810.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\175384154429810.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_010", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "11:07"], "SummaryContent": "Reminder: Submit your report by end of day.", "isRead": false, "EncryptedFrom": "<EMAIL>"}, {"id": "175384154429811", "fileName": "175384154429811.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\175384154429811.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_011", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-31", "17:16"], "SummaryContent": "New policy update effective immediately.", "isRead": false, "EncryptedFrom": "<EMAIL>"}, {"id": "175384154429813", "fileName": "175384154429813.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\175384154429813.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_013", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-31", "04:57"], "SummaryContent": "Please review the attached documents.", "isRead": false, "EncryptedFrom": "<EMAIL>"}, {"id": "17538415436981", "fileName": "17538415436981.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436981.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_001", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-27", "06:56"], "SummaryContent": "Thank you for your continued support.", "isRead": false, "EncryptedFrom": "XFSQ9YAb7vKHdYh565HtRBUmk/wfIsm8NFBT01tzn0A="}, {"id": "175384154369810", "fileName": "175384154369810.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\175384154369810.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_010", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-29", "07:34"], "SummaryContent": "New policy update effective immediately.", "isRead": true, "readAt": "2025-07-31T04:33:45.395Z", "EncryptedFrom": "RRMX94QsRkupRNsQgtx7XT7uYXYm9svjTlidC+7q/OY="}, {"id": "175384154369811", "fileName": "175384154369811.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\175384154369811.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_011", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-20", "00:48"], "SummaryContent": "Please review the attached documents.", "isRead": false, "EncryptedFrom": "2PwC+ixbyDP+uvqXiOo1V+wbWRxbQbvfcDAHitpIV0U="}, {"id": "175384154369812", "fileName": "175384154369812.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\175384154369812.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_012", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-29", "02:27"], "SummaryContent": "Your feedback on the proposal is requested.", "isRead": false, "EncryptedFrom": "2PwC+ixbyDP+uvqXiOo1V+wbWRxbQbvfcDAHitpIV0U="}, {"id": "175384154369813", "fileName": "175384154369813.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\175384154369813.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_013", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-25", "07:02"], "SummaryContent": "Thank you for your continued support.", "isRead": false, "EncryptedFrom": "RRMX94QsRkupRNsQgtx7XT7uYXYm9svjTlidC+7q/OY="}, {"id": "175384154369814", "fileName": "175384154369814.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\175384154369814.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_014", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-26", "22:48"], "SummaryContent": "Reminder: Submit your report by end of day.", "isRead": false, "EncryptedFrom": "XFSQ9YAb7vKHdYh565HtRBUmk/wfIsm8NFBT01tzn0A="}, {"id": "175384154369815", "fileName": "175384154369815.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\175384154369815.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_015", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-23", "22:19"], "SummaryContent": "Action required: Complete the training module.", "isRead": false, "EncryptedFrom": "2PwC+ixbyDP+uvqXiOo1V+wbWRxbQbvfcDAHitpIV0U="}, {"id": "175384154369816", "fileName": "175384154369816.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\175384154369816.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_016", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-26", "04:31"], "SummaryContent": "Reminder: Submit your report by end of day.", "isRead": false, "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": "17538415436982", "fileName": "17538415436982.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436982.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_002", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-24", "15:58"], "SummaryContent": "Thank you for your continued support.", "isRead": false, "EncryptedFrom": "wSU15CA8hCNDxsWSHhSH+Ucw2db3e08j1uk9YrUgv/4="}, {"id": "17538415436983", "fileName": "17538415436983.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436983.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_003", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-27", "09:44"], "SummaryContent": "Your feedback on the proposal is requested.", "isRead": false, "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": "17538415436984", "fileName": "17538415436984.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436984.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_004", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-28", "10:43"], "SummaryContent": "Please review the attached documents.", "isRead": true, "readAt": "2025-07-31T07:59:14.072Z", "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": "17538415436985", "fileName": "17538415436985.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436985.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_005", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-23", "11:29"], "SummaryContent": "Thank you for your continued support.", "isRead": false, "EncryptedFrom": "wSU15CA8hCNDxsWSHhSH+Ucw2db3e08j1uk9YrUgv/4="}, {"id": "17538415436986", "fileName": "17538415436986.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436986.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_006", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-30", "23:59"], "SummaryContent": "Reminder: Submit your report by end of day.", "isRead": true, "readAt": "2025-07-31T04:45:25.385Z", "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "17538415436987", "fileName": "17538415436987.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436987.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_007", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-24", "20:00"], "SummaryContent": "Action required: Complete the training module.", "isRead": false, "EncryptedFrom": "wSU15CA8hCNDxsWSHhSH+Ucw2db3e08j1uk9YrUgv/4="}, {"id": "17538415436988", "fileName": "17538415436988.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436988.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_008", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-20", "20:38"], "SummaryContent": "Please review the attached documents.", "isRead": false, "EncryptedFrom": "wSU15CA8hCNDxsWSHhSH+Ucw2db3e08j1uk9YrUgv/4="}, {"id": "17538415436989", "fileName": "17538415436989.json", "filePath": "C:\\classifyMail\\QuaHan\\chuaRep\\17538415436989.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "expired_009", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-29", "03:55"], "SummaryContent": "Your feedback on the proposal is requested.", "isRead": true, "readAt": "2025-07-31T04:35:10.188Z", "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "17538415442981", "fileName": "17538415442981.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\17538415442981.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "valid_001", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-31", "18:02"], "SummaryContent": "New policy update effective immediately.", "isRead": true, "readAt": "2025-07-31T04:34:13.319Z", "EncryptedFrom": "<EMAIL>", "expiredDate": ["2025-08-05", "00:34:04.862Z"]}, {"id": "17538415442986", "fileName": "17538415442986.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\17538415442986.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "valid_006", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "18:37"], "SummaryContent": "Reminder: Submit your report by end of day.", "isRead": false, "EncryptedFrom": "<EMAIL>", "expiredDate": ["2025-08-05", "00:33:37.102Z"]}, {"id": "175384154429815", "fileName": "175384154429815.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\175384154429815.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "valid_015", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "20:05"], "SummaryContent": "New policy update effective immediately.", "isRead": true, "readAt": "2025-07-31T04:34:18.409Z", "EncryptedFrom": "<EMAIL>", "expiredDate": ["2025-08-05", "00:33:37.099Z"]}, {"id": "175384154369820", "fileName": "175384154369820.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369820.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_020", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-28", "07:47"], "SummaryContent": "New policy update effective immediately.", "idrep": "8108724891", "isRead": true, "readAt": "2025-07-30T04:16:00.0000", "EncryptedFrom": "RRMX94QsRkupRNsQgtx7XT7uYXYm9svjTlidC+7q/OY="}, {"id": "175384154369821", "fileName": "175384154369821.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369821.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_021", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-27", "01:49"], "SummaryContent": "Your feedback on the proposal is requested.", "idrep": "7909038224", "isRead": true, "readAt": "2025-07-29T00:12:00.0000", "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "175384154369822", "fileName": "175384154369822.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369822.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_022", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-22", "13:20"], "SummaryContent": "This is a sample email for testing purposes.", "idrep": "7329430829", "isRead": true, "readAt": "2025-07-24T05:13:00.0000", "EncryptedFrom": "wSU15CA8hCNDxsWSHhSH+Ucw2db3e08j1uk9YrUgv/4="}, {"id": "175384154369823", "fileName": "175384154369823.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369823.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_023", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-21", "18:50"], "SummaryContent": "Invitation to the quarterly team meeting.", "idrep": "7500723138", "isRead": true, "readAt": "2025-07-23T09:14:00.0000", "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": "175384154369824", "fileName": "175384154369824.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369824.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_024", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-28", "23:19"], "SummaryContent": "Thank you for your continued support.", "idrep": "4229406235", "isRead": true, "readAt": "2025-07-30T04:56:00.0000", "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": "175384154369825", "fileName": "175384154369825.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369825.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_025", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-23", "02:33"], "SummaryContent": "New policy update effective immediately.", "idrep": "6374665287", "isRead": true, "readAt": "2025-07-24T09:29:00.0000", "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "175384154369826", "fileName": "175384154369826.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369826.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_026", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-27", "07:44"], "SummaryContent": "Please confirm your availability for the event.", "idrep": "3886246765", "isRead": true, "readAt": "2025-07-28T19:35:00.0000", "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": "175384154369828", "fileName": "175384154369828.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369828.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_028", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-22", "03:56"], "SummaryContent": "Your feedback on the proposal is requested.", "idrep": "3981126819", "isRead": true, "readAt": "2025-07-23T21:54:00.0000", "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "175384154369829", "fileName": "175384154369829.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369829.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_029", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-22", "05:54"], "SummaryContent": "Please review the attached documents.", "idrep": "1056117043", "isRead": true, "readAt": "2025-07-23T18:14:00.0000", "EncryptedFrom": "XFSQ9YAb7vKHdYh565HtRBUmk/wfIsm8NFBT01tzn0A="}, {"id": "175384154369830", "fileName": "175384154369830.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369830.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_030", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-27", "22:40"], "SummaryContent": "Please confirm your availability for the event.", "idrep": "4493517533", "isRead": true, "readAt": "2025-07-29T18:38:00.0000", "EncryptedFrom": "2PwC+ixbyDP+uvqXiOo1V+wbWRxbQbvfcDAHitpIV0U="}, {"id": "175384154369831", "fileName": "175384154369831.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369831.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_031", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-28", "01:59"], "SummaryContent": "Please review the attached documents.", "idrep": "3782796808", "isRead": true, "readAt": "2025-07-29T21:40:00.0000", "EncryptedFrom": "6lbIZhZBSjNe3As2mcGXAXe93UgVxH0EXQqd9UdXHUQ="}, {"id": "175384154369832", "fileName": "175384154369832.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369832.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_032", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-20", "18:37"], "SummaryContent": "New policy update effective immediately.", "idrep": "0352504848", "isRead": true, "readAt": "2025-07-22T12:29:00.0000", "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "175384154369833", "fileName": "175384154369833.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369833.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_033", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-22", "11:18"], "SummaryContent": "Reminder: Submit your report by end of day.", "idrep": "0056825643", "isRead": true, "readAt": "2025-07-23T22:09:00.0000", "EncryptedFrom": "XFSQ9YAb7vKHdYh565HtRBUmk/wfIsm8NFBT01tzn0A="}, {"id": "175384154369834", "fileName": "175384154369834.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369834.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_034", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-22", "23:30"], "SummaryContent": "Your feedback on the proposal is requested.", "idrep": "5829288906", "isRead": true, "readAt": "2025-07-24T19:31:00.0000", "EncryptedFrom": "b/7UDPbKAf1d261BbVNEUSKnEkCo7fPaMFe0gqdBDMH3MI5QKEPCmUaBHFwMQqdd"}, {"id": "175384154369835", "fileName": "175384154369835.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369835.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_035", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-21", "07:37"], "SummaryContent": "Invitation to the quarterly team meeting.", "idrep": "1991472709", "isRead": true, "readAt": "2025-07-22T14:12:00.0000", "EncryptedFrom": "XFSQ9YAb7vKHdYh565HtRBUmk/wfIsm8NFBT01tzn0A="}, {"id": "175384154369836", "fileName": "175384154369836.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369836.json", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_036", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-20", "17:41"], "SummaryContent": "Reminder: Submit your report by end of day.", "idrep": "2833180689", "isRead": true, "readAt": "2025-07-22T16:25:00.0000", "EncryptedFrom": "wSU15CA8hCNDxsWSHhSH+Ucw2db3e08j1uk9YrUgv/4="}, {"id": "175384154369827", "fileName": "175384154369827.json", "filePath": "C:\\classifyMail\\QuaHan\\daRep\\175384154369827.json", "category": "ReviewMail", "status": "da<PERSON><PERSON>", "isExpired": true, "isReplied": true, "Subject": "expired_027", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-29", "15:35"], "SummaryContent": "Meeting reminder: Project update discussion.", "idrep": "2632467354", "isRead": true, "readAt": "2025-07-31T06:32:00.0000", "originalCategory": "<PERSON><PERSON><PERSON><PERSON>", "originalStatus": "da<PERSON><PERSON>", "dateMoved": ["2025-07-31", "11:46"], "EncryptedFrom": "wSU15CA8hCNDxsWSHhSH+Ucw2db3e08j1uk9YrUgv/4="}, {"id": 46, "fileName": "valid_003.json", "filePath": "C:\\classifyMail\\ReviewMail\\valid_003.json", "category": "ReviewMail", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_003", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-31", "17:51"], "SummaryContent": "Please review the attached documents.", "isRead": true, "readAt": "2025-07-31T06:15:45.541Z", "assignedTo": {"type": "pic", "groupId": null, "picId": "1753781583113", "assignedAt": "2025-07-31T06:16:15.855Z", "picName": "<PERSON>", "picEmail": "<EMAIL>"}, "updatedAt": "2025-07-31T06:16:15.855Z", "EncryptedFrom": "<EMAIL>", "dateMoved": ["2025-08-05", "10:44"], "originalCategory": "<PERSON><PERSON><PERSON><PERSON>", "originalStatus": "mustRep"}, {"id": "17538415442986", "fileName": "17538415442986.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\17538415442986.json", "category": "ReviewMail", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "valid_006", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "18:37"], "SummaryContent": "Reminder: Submit your report by end of day.", "isRead": false, "EncryptedFrom": "<EMAIL>", "expiredDate": ["2025-08-05", "00:33:37.102Z"], "originalCategory": "<PERSON><PERSON><PERSON><PERSON>", "originalStatus": "chua<PERSON><PERSON>", "dateMoved": ["2025-08-05", "07:34"]}, {"id": "175384154429812", "fileName": "175384154429812.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\175384154429812.json", "category": "ReviewMail", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_012", "From": "<EMAIL>", "Type": "To", "Date": ["2025-07-31", "21:04"], "SummaryContent": "Your feedback on the proposal is requested.", "isRead": true, "assignedTo": {"type": "pic", "groupId": null, "picId": "1753781583113", "assignedAt": "2025-07-31T04:43:01.531Z", "picName": "<PERSON>", "picEmail": "<EMAIL>"}, "updatedAt": "2025-07-31T04:43:01.531Z", "readAt": "2025-07-31T06:15:49.101Z", "EncryptedFrom": "<EMAIL>", "originalCategory": "<PERSON><PERSON><PERSON><PERSON>", "originalStatus": "mustRep", "dateMoved": ["2025-07-31", "13:21"]}, {"id": 49, "fileName": "valid_014.json", "filePath": "C:\\classifyMail\\ReviewMail\\valid_014.json", "category": "ReviewMail", "status": "mustRep", "isExpired": false, "isReplied": false, "Subject": "valid_014", "From": "<EMAIL>", "Type": "CC", "Date": ["2025-07-31", "17:31"], "SummaryContent": "This is a sample email for testing purposes.", "isRead": false, "EncryptedFrom": "<EMAIL>", "dateMoved": ["2025-08-05", "23:51"], "originalCategory": "<PERSON><PERSON><PERSON><PERSON>", "originalStatus": "mustRep"}, {"id": "175384154429815", "fileName": "175384154429815.json", "filePath": "C:\\classifyMail\\DungHan\\mustRep\\175384154429815.json", "category": "ReviewMail", "status": "chua<PERSON><PERSON>", "isExpired": true, "isReplied": false, "Subject": "valid_015", "From": "<EMAIL>", "Type": "BCC", "Date": ["2025-07-31", "20:05"], "SummaryContent": "New policy update effective immediately.", "isRead": true, "readAt": "2025-07-31T04:34:18.409Z", "EncryptedFrom": "<EMAIL>", "expiredDate": ["2025-08-05", "00:33:37.099Z"], "originalCategory": "<PERSON><PERSON><PERSON><PERSON>", "originalStatus": "chua<PERSON><PERSON>", "dateMoved": ["2025-08-05", "07:34"]}]